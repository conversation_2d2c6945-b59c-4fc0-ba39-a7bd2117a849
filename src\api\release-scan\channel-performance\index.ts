import { defHttp } from '/@/utils/http/axios';

enum Api {
    // shopBehavior = '/shop/shopBehavior',
    getAllDevice = '/shop/getAllDevice',
    analysisDownloadsIncomeByAppId = '/shop/analysisDownloadsIncomeByAppId',
    getAllCountry = '/shop/getAllCountry',
    top = '/shop/topStore/top',
    gameActivityAnalysis = '/releaseScans/storeAndChannelPerformance/gameActivityAnalysis',
    globalHeat = '/releaseScans/storeAndChannelPerformance/globalHeat',

}

// 应用商店排行数据
export function topApi(data){
    return defHttp.post({
        url:Api.top,
        data
    })
}


// 获取输入框设备类型
export function getAllDeviceApi(){
    return defHttp.get({
        url: Api.getAllDevice,
    })
}

// 获取输入框国家类型
export function getAllCountryApi(){
    return defHttp.get({
        url: Api.getAllCountry
    })
}

// 获取游戏活跃度分析数据
export function gameActivityAnalysisApi(params){
    return defHttp.get({
        url: Api.gameActivityAnalysis,
        params
    })
}

// 全球热度分布数据
export function globalHeatApi(data){
    return defHttp.post({
        url: Api.globalHeat,
        data
    })
}


