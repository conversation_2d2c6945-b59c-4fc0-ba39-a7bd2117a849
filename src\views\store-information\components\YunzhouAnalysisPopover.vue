<template>
    <a-popover 
      placement="topLeft" 
      trigger="click"
      :overlayClassName="'custom-tooltip'"
      @visibleChange="handlePopoverVisibleChange"
    >
      <template #content>
        <div style="max-width: 300px;margin: 10px;">
          <a-row>
            <a-col :span="3"><img style="padding-top: 5px;" :src="avatarUrl" alt=""></a-col>
            <a-col :span="10">
              <div class="ai-header">
                <div class="ai-title">{{ title }}</div>
                <div class="ai-updateTime">更新于{{ updateTime }}</div>
              </div>
            </a-col>
            <a-col :span="11" class="ai-analyze">{{ aiLabel }}</a-col>
          </a-row>
          <div class="ai-content" ref="aiContentRef">
            <a-spin :spinning="loading">
              <div v-html="formattedContent"></div>
            </a-spin>
          </div>
          <div class="ai-evaluate" v-if="showRating">
            评价此分析：<a-rate v-model:value="starRating" />
          </div>
        </div>
      </template>
      
      <!-- 默认插槽，允许自定义触发元素 -->
      <slot>
        <!-- 默认触发元素 -->
        <span><img style="width: 60px; height: 30px;" :src="triggerIcon" alt=""></span>
      </slot>
    </a-popover>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onUnmounted, nextTick } from 'vue';
  import { marked } from 'marked';
  import { useGlobSetting } from '@/hooks/setting';
  // 在组件顶部添加图片导入
  import triggerIcon from '@/views/store-information/market-dynamics/popular-ranking/pic/ai-analysis.png';
  import avatar      from '@/views/store-information/market-dynamics/popular-ranking/pic/ai-avatar.png';

  
  const globSetting = useGlobSetting();
  
  const props = defineProps({
    // 分析内容
    content: {
      type: String,
      default: ''
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 标题
    title: {
      type: String,
      default: '云舟智读'
    },
    // 更新时间
    updateTime: {
      type: String,
      default: '1分钟前'
    },
    // AI标签
    aiLabel: {
      type: String,
      default: 'AI分析'
    },
    // 头像URL
    avatarUrl: {
      type: String,
      default: avatar 
    },
    // 触发图标URL
    triggerIcon: {
      type: String,
      default: triggerIcon
    },
    // 是否显示评分
    showRating: {
      type: Boolean,
      default: false
    },
    // 获取分析内容的方法
    fetchAnalysis: {
      type: Function,
      default: () => {}
    }
  });
  
  const emit = defineEmits(['update:content', 'update:loading', 'rate']);
  
  const aiContentRef = ref<HTMLElement | null>(null);
  const starRating = ref(0);

  const formattedContent = computed(() => {
    if (!props.content) return '';
    // 清理掉 data: 前缀、多余空格等
    const cleaned = cleanUnwantedSymbols(props.content);
    // 把字面 "\n" 还原成换行
    const withNewlines = cleaned.replace(/\\n/g, '\n');
    return marked(withNewlines);
    });

  // 清理无用符号
  function cleanUnwantedSymbols(text) {
    // 1. 去掉每行开头的 "data:" 前缀（可能多个）
    text = text.replace(/^(data:\s*)+/gm, '');
    
    // 2. 保留Markdown格式标记（###、**等）和换行符
    // 不需要额外处理，这些格式需要保留
    
    // 3. 处理多余的空格和特殊符号
    text = text.replace(/\s{2,}/g, ' ');  // 多个空格合并为一个
    text = text.replace(/[^\S\r\n]+$/gm, ''); // 去掉行尾空格

    return text
  }
  
  // 格式化内容（支持Markdown）
//   const formattedContent = computed(() => {
//     if (!props.content) return '';
//     const withRealNewlines = props.content.replace(/\\n/g, '\n');
//     return marked(withRealNewlines);
//   });

  // const formattedAIAnalysisContent = computed(() => {
//   if (!aiAnalysisContent.value) return ''
//   // 1. 转义掉字面 \n
//   const withRealNewlines = aiAnalysisContent.value.replace(/\\n/g, '\n')
//   // 2. 交给 marked 生成 HTML
//   return marked(withRealNewlines)
// })
  
  // 处理弹框可见性变化
  function handlePopoverVisibleChange(visible: boolean) {
    if (visible) {
      // 调用父组件传入的获取分析内容的方法
      props.fetchAnalysis();
      nextTick(() => {
        // 确保内容区域滚动到底部
        if (aiContentRef.value) {
          aiContentRef.value.scrollTop = aiContentRef.value.scrollHeight;
        }
      });
    }
  }

  // function handlePopoverVisibleChange(visible: boolean) {
//   if (visible) {
//     fetchAIAnalysis();
//   } else {
//     // 如果需要支持中途取消，可以用 AbortController
//   }
// }

  
  // 清理函数（如果需要）
  onUnmounted(() => {
    // 清理资源
  });
  </script>
  
  <style scoped lang="scss">
  // 智能分析弹框样式
  :deep(.custom-tooltip) {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
  }
  
  .ai-header {
    margin-left: 10px;
  }
  
  .ai-title {
    font-weight: bolder;
    font-size: 15px;
  }
  
  .ai-updateTime {
    font-size: 10px;
    color: #d9d9d9;
  }
  
  .ai-analyze {
    display: flex; 
    justify-content: flex-end; 
    align-items: center; 
    color: #37b2e7;
  }
  
  .ai-content {
    max-height: 300px;
    min-height: 100px;
    overflow-y: auto;
    padding-right: 8px;
    margin-top: 5px;
    color: #898b8c;
    
    // 内容样式
    :deep(h1), :deep(h2), :deep(h3) {
      margin-top: 0.5em;
      margin-bottom: 0.3em;
      font-weight: bold;
    }
    
    :deep(p) {
      margin: 0.5em 0;
      line-height: 1.5;
    }
    
    :deep(ul), :deep(ol) {
      padding-left: 1.5em;
      margin: 0.5em 0;
    }
    
    :deep(li) {
      margin: 0.3em 0;
    }
    
    :deep(strong) {
      font-weight: bold;
    }
    
    :deep(em) {
      font-style: italic;
    }
  }
  
  .ai-evaluate {
    margin-top: 10px;
    color: #d9d9d9;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  </style>