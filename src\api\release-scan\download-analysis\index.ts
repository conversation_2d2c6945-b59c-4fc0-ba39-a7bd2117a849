import { defHttp } from '/@/utils/http/axios';

enum Api{
    getAllDevice = '/shop/getAllDevice',
    // getDownloadCompare = '/shop/getDownloadCompare',
    getDownloadCompareRank = '/shop/getDownloadCompareRank',
    getAllCountry = '/shop/getAllCountry',
    // downloadVolume = '/releaseScans/downloadDataAnalysis/downloadVolume',
    gameDimension = '/shop/competitorAnalysis/gameDimension',
    appList = '/shop/appLeaderboards/appList'
}

// 获取设备列表
export function getAllDeviceApi(){
    return defHttp.get({
        url: Api.getAllDevice
    })
}

// 获取国家列表
export function getAllCountryApi(){
    return defHttp.get({
        url: Api.getAllCountry
    })
}

// 获取下载量分析
export function gameDimensionApi(data){
    return defHttp.post({
        url: Api.gameDimension,
        data
    })
}

// 获取下载来源分析
export function appListApi(data){
    return defHttp.post({
        url: Api.appList,
        data
    })
}

