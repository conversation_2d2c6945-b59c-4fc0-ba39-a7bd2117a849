import { defHttp } from '/@/utils/http/axios';

enum Api {
    queryById = '/appInfo/queryById',
    getComment = '/shop/getComment',
    getCommentCount = '/shop/getCommentCount',
    getRankings = '/appInfo/rankings'


}

// 获取应用详情
export function queryByIdApi(params){
  return defHttp.get({
    url: Api.queryById,
    params
  })
}

// 获取评论列表
export function getCommentApi(params){
  return defHttp.get({
    url: Api.getComment,
    params
  })
}

// 获取评分
export function getCommentCountApi(params){
  return defHttp.get({
    url: Api.getCommentCount,
    params
  })
}

// 获取游戏排名趋势
export function getRankingsApi(params){
  return defHttp.get({
    url: Api.getRankings,
    params
  })
}

