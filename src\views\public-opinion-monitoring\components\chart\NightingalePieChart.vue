<template>
  <div class="chart-wrapper" :style="{ height, width }">
    <!-- 显示全部按钮 -->
    <a-button
      v-if="showToggleButton"
      class="chart-toggle-btn"
      @click="toggleShowAllData"
    >
      {{ internalShowAllData ? '去零显示' : '显示全部' }}
    </a-button>

    <!-- ECharts图表容器 -->
    <div
      ref="chartRef"
      :style="{ height: '100%', width: '100%' }"
      class="chart-container"
    >
      <!-- 当没有有效数据时显示的空状态 -->
      <div v-if="showEmptyState" class="empty-state">
        <div class="empty-icon">📊</div>
        <div class="empty-text">暂无数据</div>
        <div class="empty-subtext">当前时间段内没有舆情数据</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, PropType, ref, Ref, watchEffect } from 'vue';
import { useECharts } from '@/hooks/web/useECharts';
export default defineComponent({
  name: '<PERSON><PERSON>ie<PERSON><PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => [],
    },
    width: {
      type: String as PropType<string>,
      default: '100%',
    },
    height: {
      type: String as PropType<string>,
      default: 'calc(100vh - 208px)',
    },
    showAllData: {
      type: Boolean,
      default: false,
    },
    showToggleButton: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const chartRef = ref<HTMLDivElement | null>(null);
    const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);
    const showEmptyState = ref(false);

    // 内部状态管理显示全部数据
    const internalShowAllData = ref(props.showAllData);

    // 切换显示全部数据的函数
    const toggleShowAllData = () => {
      internalShowAllData.value = !internalShowAllData.value;
    };

    function initCharts() {
      // 检查原始数据是否为空
      if (!props.chartData || props.chartData.length === 0) {
        showEmptyState.value = true;
        // 清空图表
        setOptions({
          series: [{
            type: 'pie',
            data: []
          }]
        });
        return;
      }

      // 根据internalShowAllData状态决定是否过滤0值数据
      const filteredData = internalShowAllData.value
        ? props.chartData
        : props.chartData.filter((item: any) => item.value > 0);

      // 检查过滤后是否有有效数据
      if (filteredData.length === 0) {
        showEmptyState.value = true;
        // 清空图表
        setOptions({
          series: [{
            type: 'pie',
            data: []
          }]
        });
        return;
      } else {
        showEmptyState.value = false;
      }

      let option: any = {
        legend: {
          top: 'bottom',
          itemWidth: 12,
          itemHeight: 9,
          // 图例类型设置为可滚动
          type: 'scroll' as const,
          // 图例布局为横向
          orient: 'horizontal' as const,
          // 翻页按钮箭头大小
          pageIconSize: 12,
          // 翻页按钮与图例项之间的距离
          pageButtonItemGap: 6,
          // "1/2" 页码的颜色
          pageTextStyle: {
            color: '#999',
          },
          textStyle: {
            lineHeight: 20, // 增加行高
          },
          // 限制图例宽度
          width: '90%',
        },
        tooltip: {
          trigger: 'item' as const,
          formatter: '{b}: {c} ({d}%)'
        },
        series: {
          name: '舆情渠道',
          type: 'pie' as const,
          radius: '55%',
          center: ['50%', '40%'],
          // roseType: 'area', // 南丁格尔图，使用面积比例
          roseType: 'radius' as const,
          data: filteredData,
          label: {
            show: true,
            position: 'outside' as const,
            formatter: '{b}\n{d}%',
            avoidLabelOverlap: true,
          },
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      };
      setOptions(option);
      resize();
    }

    watchEffect(() => {
      // 无论数据是否为空都要调用initCharts，让它自己处理空数据情况
      initCharts();
    });

    // 监听internalShowAllData变化，重新渲染图表
    watchEffect(() => {
      // 无论数据是否为空都要调用initCharts
      initCharts();
    });

    return {
      chartRef,
      showEmptyState,
      internalShowAllData,
      toggleShowAllData
    };
  },
});
</script>

<style scoped>
.chart-wrapper {
  position: relative;
}

.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #999;
  z-index: 10;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}

/* 显示全部按钮样式 */
.chart-toggle-btn {
  position: absolute;
  top: 8px; right: 12px;
  z-index: 20;
  padding: 4px 8px;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 2px;
  cursor: pointer;
}
</style>
