import { defHttp } from '/@/utils/http/axios';

enum Api {
  getAllDevice = '/shop/getAllDevice',
  getDownloadCompare = '/shop/getDownloadCompare',
  getPublisherInfo3 = '/shop/getPublisherInfo3',
  getAllCountry = '/shop/getAllCountry',
}

/**
 * @description: 获取所有设备列表
 * @returns 设备列表数据
 */
export function getAllDeviceApi() {
  return defHttp.get({
    url: Api.getAllDevice,
  });
}

/**
 * @description: 获取游戏下载量对比数据
 * @param params 请求参数
 * @returns 游戏下载量对比数据
 */
export function getDownloadCompareApi(params) {
  return defHttp.get({
    url: Api.getDownloadCompare,
    params,
  });
}

/**
 * @description: 获取发行商信息
 * @param params 请求参数
 * @returns 发行商信息数据
 */
export function getPublisherInfo3Api(params) {
  return defHttp.get({
    url: Api.getPublisherInfo3,
    params,
  });
}

/**
 * @description: 获取所有国家类型
 * @returns 国家类型列表数据
 */
export function getAllCountryApi() {
  return defHttp.get({
    url: Api.getAllCountry,
  });
}

/**
 * @description: 游戏维度分析 - 根据游戏appid列表按时间颗粒度查询下载量和收入
 */
export function gameDimensionApi(params: {
  countryNames?: string[];
  platformNames?: string[];
  appIdList: string[];
  startTime: string;
  endTime: string;
  granularity: 'day' | 'week' | 'month';
}) {
  return defHttp.post({
    url: '/shop/competitorAnalysis/gameDimension',
    data: params,
  });
}