import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetAllDevice = '/shop/getAllDevice',
  GetMutDim = '/shop/getMutDim',
  GetAllCountry = '/shop/getAllCountry',
  Popularity = '/releaseScans/marketingTesting/popularity',
}


/**
 * 获取所有设备平台
 */
export function getAllDeviceApi() {
  return defHttp.get({
    url: Api.GetAllDevice,
  });
}


/**
 * 获取多维度统计数据
 * @param params 查询参数
 */
export function getMutDimApi(params: {
  publisherId: string;
  sortField: string;
  startTime: string;
  country: string;
  device: string;
}) {
  return defHttp.get({
    url: Api.GetMutDim,
    params,
  });
}

/**
 * 获取所有国家类型
 */
export function getAllCountryApi() {
  return defHttp.get({
    url: Api.GetAllCountry,
  });
}

// 推广活动热度统计接口
export function getPopularityApi(data: {
  appIdList: string[];
  startTime: string;
  endTime: string;
  countryNames?: string[];
  platformNames?: string[];
}) {
  return defHttp.post({
    url: Api.Popularity,
    data,
  });
}