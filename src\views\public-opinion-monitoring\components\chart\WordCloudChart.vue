<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, Ref, watchEffect } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  export default defineComponent({
    name: 'WordCloudChart',
    props: {
      chartData: {
        type: Array as PropType<Array<{ title: string; views: number }>>,
        default: () => [],
      },
      width: {
        type: String as PropType<string>,
        default: '100%',
      },
      height: {
        type: String as PropType<string>,
        default: '100%',
      },
    },
    setup(props) {
      const chartRef = ref<HTMLDivElement | null>(null);
      const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);

      function initCharts() {
        // 检查是否有有效数据
        if (!props.chartData || props.chartData.length === 0) {
          // 如果没有数据，清空图表
          setOptions({
            series: [{
              type: 'wordCloud',
              data: []
            }]
          });
          return;
        }

        const newsWordCloudData = props.chartData.map((item) => ({
          name: item.title,
          value: parseInt(item.views) || 1, // 确保有默认值，避免 NaN
        }));

        let option = {
          tooltip: { show: true },
          series: [
            {
              type: 'wordCloud',
              shape: 'circle',
              sizeRange: [10, 25],    //减小最大字体
              rotationRange: [-15, 15],   //允许词语旋转
              rotationStep: 45,
              gridSize: 8,   //增加网格间距
              drawOutOfBound: true,    //允许绘制到边界外
              textStyle: {
                color: () => {
                  return `rgb(${Math.round(Math.random() * 255)}, ${Math.round(Math.random() * 255)}, ${Math.round(Math.random() * 255)})`;
                },
              },
              data: newsWordCloudData,
            },
          ],
        };
        setOptions(option);
        resize();
      }

      watchEffect(() => {
        // 无论数据是否为空都要调用initCharts，让它自己处理空数据情况
        initCharts();
      });

      return { chartRef };
    },
  });
</script>
